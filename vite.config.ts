import { fileURLToPath, URL } from 'node:url'

import { defineConfig, type ConfigEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import UnoCSS from 'unocss/vite'

import vueDevTools from 'vite-plugin-vue-devtools'
import AutoImport from 'unplugin-vue-components/vite'
import Components from 'unplugin-vue-components/vite'
import { TDesignResolver } from 'unplugin-vue-components/resolvers'
import { codeInspectorPlugin } from 'code-inspector-plugin';
import { join } from 'path'

export const buildConfig = (config: ConfigEnv) => {
  console.log(`当前环境:「${config.mode}」`);
  return  {
    base: './',
    server: {
      port: 6001,
    },
    build: {
      sourcemap: config.mode !== 'release',
      emptyOutDir: true,
      outDir: join(__dirname, '/src-utools/dist'),
    },
    plugins: [
      vue(),
      vueJsx(),
      vueDevTools(),
      UnoCSS(),
      // vueDevTools(),
      AutoImport({
        resolvers: [TDesignResolver({
          library: 'vue-next'
        })],
      }),
      Components({
        resolvers: [
          TDesignResolver({
            library: 'vue-next',
          })
        ]
      }),
      codeInspectorPlugin({
        bundler: 'vite',
      }),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      },
    },
  };
}

// https://vite.dev/config/
export default defineConfig(buildConfig);
