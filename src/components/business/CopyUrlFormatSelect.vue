<script setup lang="ts">
import { toRefs } from 'vue'
import { useUserSettingStore } from '@/stores'

const { autoCopyUrlFormat, enableFormatUrlData } = toRefs(useUserSettingStore());
</script>

<template>
  <t-select v-model:value="autoCopyUrlFormat"
            style="width: 170px"
            size="small"
            clearable>
    <template #prefixIcon>
      <div class="text-sm">自动复制</div>
    </template>
    <t-option v-for="(formatUrlData) in enableFormatUrlData"
              :key="formatUrlData.id"
              :value="formatUrlData.id"
              :label="formatUrlData.displayName" />
  </t-select>
</template>

<style scoped lang="less">

</style>
