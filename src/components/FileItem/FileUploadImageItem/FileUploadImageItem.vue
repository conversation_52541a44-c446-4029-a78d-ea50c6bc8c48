<script setup lang="ts">
import { type FileLibraryItem, useUserSettingStore } from '@/stores'
import { FilePreview } from '@/components/FilePreview'
import LazyImg from '@/components/FilePreview/preview/LazyImg.vue'
import { useTemplateRef } from 'vue'
import { useFileBoxStore, useStorageSourceStore, useUploadFileListStore } from '@/stores'
import { MessagePlugin } from 'tdesign-vue-next'
import { fileLibraryConvertUrl } from '@/utils/UrlFormatBuilder.ts'

defineProps<{
  fileLibrary: FileLibraryItem
}>();

const uploadFileListStore = useUploadFileListStore()
const storageSourceStore = useStorageSourceStore()
const fileBoxStore = useFileBoxStore()
const emits = defineEmits<{
  afterDelete: [id: string]
}>()

async function handleFileDelete(fileLibrary: FileLibraryItem, remote: boolean) {
  if (remote) {
    // 远程删除
    for (const fileUploadInfo of fileLibrary.fileUploadInfoList) {
      const storageSource = storageSourceStore.getStorageSourceById(fileUploadInfo.storageId)
      if (storageSource) {
        try {
          await window.storagePlugInManager
            .getPluginInstance(storageSource.storagePluginCode)
            .deleteFile(fileUploadInfo)
        } catch (e) {
          console.log("远程删除出现异常", e);
          debugger
          return
        }
      } else {
        // 不存在
      }
    }
  }
  // 插件内文件数据移除
  uploadFileListStore.removeFileHistoryItemById(fileLibrary.id)
  fileBoxStore.removeFileLibrary(fileLibrary)
  emits('afterDelete', fileLibrary.id)
}

function handleCopyText(url: string) {
  utools.copyText(url)
  MessagePlugin.success('复制成功')
}
const { enableFormatUrlData } = useUserSettingStore()
const fileUploadImageRef = useTemplateRef('fileUploadImageRef')
</script>

<template>
  <div ref="fileUploadImageRef"
       class="u-pos-rel file-upload-image">
    <FilePreview
      :content="
        fileLibrary.fileUploadInfoList[0].url
          ? fileLibrary.fileUploadInfoList[0].url
          : '/image-error.svg'
      "
      :file-suffix="fileLibrary.fileInfo.fileSuffix"
    >
      <template #image="{ src }">
        <LazyImg title="" alt="" :url="src"></LazyImg>
      </template>
    </FilePreview>
    <div class="action">
      <t-popconfirm
        confirm-btn="远程删除"
        cancel-btn="插件移除"
        placement="left"
        @confirm="handleFileDelete(fileLibrary, false)"
        @cancel="handleFileDelete(fileLibrary, true)"
      >
        <template #content>
          <span class="u-font-size-smail">请选择删除方式, 放弃删除点击外部区域</span>
        </template>
        <div class="btn red">
          <t-icon class="i-u-delete" />
        </div>
      </t-popconfirm>
      <t-dropdown trigger="hover"
                  :popup-container="fileUploadImageRef!">
        <div class="btn blue">
          <t-icon class="i-u-copy" />
        </div>
        <t-dropdown-menu>
          <t-dropdown-item v-for="(formatUrlData) in enableFormatUrlData"
                           :key="formatUrlData.id"
                           @click="handleCopyText(fileLibraryConvertUrl(fileLibrary, formatUrlData.format))">
            {{ formatUrlData.displayName }}
          </t-dropdown-item>
        </t-dropdown-menu>
      </t-dropdown>
    </div>
  </div>
</template>

<style scoped lang="less">
.file-upload-image {
  &:hover {
    .action {
      opacity: 1;
    }
  }
  .action {
    position: absolute;
    right: 4px;
    top: 4px;
    border-radius: 30px;
    background-color: rgba(38, 38, 38, 0.3);
    color: rgba(255, 255, 255, 0.7);
    padding: 4px 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    opacity: 0;
    transition:
      background-color 150ms linear,
      opacity 200ms linear;
    &:hover {
      background: rgba(38, 38, 38, 0.7);
    }
    > div {
      cursor: pointer;
      transition: color 150ms linear;
      &:hover {
        color: #ffffff;
      }
    }
  }
}
</style>
