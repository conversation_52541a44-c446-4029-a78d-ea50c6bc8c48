<script setup lang="ts">
import FileUtils from '@/utils/FileUtils.ts'
import FileIconPreview from '@/components/FilePreview/FileIconPreview.vue'
import type { FileUpdateInfoItem } from '@/@types'
import { NotifyPlugin, type CascaderProps } from 'tdesign-vue-next';
import { useUploadFileListStore } from '@/stores'
import { ref } from 'vue'
import { useEventListener } from '@vueuse/core'

const props = defineProps<{
  data: FileUpdateInfoItem;
  selectUploadSourceOptions: CascaderProps['options'];
}>();

const uploadFileListStore = useUploadFileListStore();

const fileItem = ref(props.data);

useEventListener(window,`fileItemChange::${props.data.id}`, () => {
  const uploadFileItem = uploadFileListStore.getUploadFileItem(props.data.id)
  if (uploadFileItem) {
    fileItem.value = uploadFileItem;
  }
});

// 重新上传
function handleReloadUpload(uploadFileItem: FileUpdateInfoItem) {
  uploadFileListStore.reloadUploadFileItem(uploadFileItem)
}

function handleRemoveUploadItem(uploadFileItem: FileUpdateInfoItem) {
  uploadFileListStore.removeUploadItem(uploadFileItem);
  NotifyPlugin.success({
    title: '提示',
    content: '移除成功',
    closeBtn: true,
    duration: 1000
  })
}

</script>

<template>
  <div v-if="fileItem"
       class="u-fx u-f-between u-fac upload-file-item">
    <div class="file-info">
      <div class="file-preview">
        <FileIconPreview :content="fileItem.filePath ? `file://${fileItem.filePath}` : fileItem.content!"
                         :file-suffix="fileItem.fileSuffix" />
      </div>
      <div class="file-details">
        <div class="file-name">{{ fileItem.fileName }}</div>
        <div class="file-size" v-if="fileItem.fileSize">
          {{ FileUtils.formatFileSize(fileItem.fileSize) }}
        </div>
      </div>
    </div>
    <div>
      <t-tag v-if="fileItem.uploadStatus === 'waiting'">等待上传</t-tag>
      <t-loading v-else-if="fileItem.uploadStatus === 'uploading'"
                 size="small">
        <template #text>
          上传中 <span v-if="fileItem.progress" style="padding-left: 2px;">{{fileItem.progress}}%</span>
        </template>
      </t-loading>
      <t-tag v-else-if="fileItem.uploadStatus === 'failed'"
             variant="outline"
             color="red">
        上传失败
      </t-tag>
    </div>
    <div class="u-fx u-gap5" style="flex-direction: column; align-items: end;">
      <t-cascader v-if="fileItem.uploadStatus === 'failed'"
                  :options="selectUploadSourceOptions"
                  v-model:model-value="fileItem.uploadWay"
                  :style="{width:'150px'}"
                  placeholder="选择上传方式"
                  size="small"
                  filterable>
      </t-cascader>
      <div v-if="fileItem.uploadStatus === 'failed'"
           class="u-font-size-smail flex gap-5">
        <t-link size="small"
                theme="danger"
                @click="() => handleRemoveUploadItem(fileItem!)">
          <template #prefixIcon><t-icon class="i-u-delete"></t-icon></template>
          移除
        </t-link>
        <t-link size="small"
                theme="primary"
                @click="() => handleReloadUpload(fileItem!)">
          <template #prefixIcon><t-icon class="i-u-upload-one"></t-icon></template>
          重新上传
        </t-link>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
// 文件信息
.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  .file-details {
    .file-name {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .file-size {
      font-size: 12px;
      color: #86909c;
    }
  }
}
</style>
