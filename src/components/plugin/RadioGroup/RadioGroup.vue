<script setup lang="ts">
import type { StorageUIFormElement } from '@xiaou66/picture-plugin'
import { onMounted } from 'vue'

const props = defineProps<{
  formItem: StorageUIFormElement
}>()

const modelValue = defineModel<any>('model-value');

onMounted(() => {
  if (!modelValue.value) {
    modelValue.value = props.formItem.formItem.defaultValue
  }
});
</script>
<template>
  <t-radio-group v-model:value="modelValue"
                 variant="default-filled">
    <template v-for="item in formItem.formItem.data"
              :key="item.value">
      <t-tooltip v-if="item.tooltip">
        <template #content>
          {{ item.tooltip }}
        </template>
        <t-radio-button :value="item.value">
          {{ item.label }}
        </t-radio-button>
      </t-tooltip>
      <t-radio-button v-else
               :value="item.value">
        {{ item.label }}
      </t-radio-button>
    </template>
  </t-radio-group>
</template>
<style lang="less" scoped>

</style>
