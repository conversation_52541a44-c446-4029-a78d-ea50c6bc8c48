<script setup lang="ts">
import type { StorageUIFormElement } from '@xiaou66/picture-plugin'
import { NotifyPlugin } from 'tdesign-vue-next';
const props = defineProps<{
  formItem: StorageUIFormElement
}>();

const model = defineModel<any>('model-value');

async function handleClick() {
  if (props.formItem.formItem) {
    try {
      await props.formItem.formItem.click(model)
    }catch (e: any) {
      NotifyPlugin.error({
        title: `${props.formItem.formItem.label} 失败`,
        content: e.message,
        closeBtn: true,
      });
    }
  }
}
</script>

<template>
  <t-input-group style="width: 100%">
    <t-input readonly v-model:value="model[formItem.formItem.name]" />
    <t-button v-bind="formItem.elementProperty"
              @click="handleClick">
      {{ formItem.formItem.buttonText || '获取' }}
    </t-button>
  </t-input-group>
</template>

<style scoped lang="less">

</style>
