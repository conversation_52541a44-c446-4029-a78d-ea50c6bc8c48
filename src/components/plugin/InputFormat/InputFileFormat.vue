<script setup lang="ts">
import type { StorageUIFormElement } from '@xiaou66/picture-plugin'
import { filePathFormat, stringVariable } from '@xiaou66/picture-plugin'
import { onMounted, ref, watch } from 'vue'
import { nanoid } from 'nanoid'
import { MessagePlugin } from 'tdesign-vue-next'
const props = defineProps<{
  formItem: StorageUIFormElement
}>();

const modelValue = defineModel<string>('model-value');
const demoFormatResult = ref('');

function formatResult() {
  if (!modelValue.value) {
    modelValue.value = props.formItem.elementProperty.defaultValue;
  }
  demoFormatResult.value = filePathFormat(modelValue.value || props.formItem.elementProperty.defaultValue || '', {
    id: nanoid(),
    storageId: nanoid(),
    sceneName: '测试',
    filePath: '',
    allFileName: 'demo.png',
    fileName: 'demo',
    suffix: 'png',
    fileSize: 100,
    file: {} as File,
  });
  console.log('demoFormatResult', demoFormatResult.value)
}
watch(() => modelValue.value, (val) => {
  formatResult();
});
onMounted(() => {
  formatResult();
})

function copyText(text: string) {
  utools.copyText(text);
  MessagePlugin.success("复制成功");
}
</script>

<template>
  <t-popup trigger="hover"
           placement="top"
           overlayClassName="u-web-popup"
           destroyOnClose>
    <template #content>
      <div class="input-format-trigger">
        <div class="u-font-size-smail">
          <div>文件路径</div>
          <div style="padding-top: 5px;">
            <div style="padding-top: 3px;">
              <span class="u-tips">{{ formItem.formItem.label }}: </span> demo.png
            </div>
            <div style="padding-top: 3px;">
              <span class="u-tips">上传后{{ formItem.formItem.label }}: </span>{{demoFormatResult}}
            </div>
          </div>
          <div class="helper">
            <div style="padding: 5px 0;">常用格式</div>
            <div class="u-fx u-gap5" style="flex-direction: column">
              <div>
                <t-link size="small"
                        theme="primary"
                        @click="() => modelValue = '{filename}.{suffix}'">{filename}.{suffix}</t-link>: demo.png
              </div>
              <div v-if="!formItem.type || formItem.formItem.type !== 'file'">
                <t-link size="small"
                        theme="primary"
                        @click="() => modelValue = '{YY}-{M}/{filename}.{suffix}'">
                  {YY}-{M}/{filename}.{suffix}
                </t-link>: 25-01/demo.png
              </div>
              <div v-if="!formItem.type || formItem.formItem.type !== 'file'">
                <t-link size="small"
                        theme="primary"
                        @click="() => modelValue = '{YY}-{M}/{filename}_{timestamp}.{suffix}'">
                  {YY}-{M}/{filename}_{timestamp}.{suffix}
                </t-link>: 25-01/demo_12332.png
              </div>
            </div>
            <div style="padding: 5px 0;">变量</div>
            <div class="u-fx u-gap5 u-fac" style="flex-wrap: wrap">
              <div v-for="variable in stringVariable" :key="variable.value"
                   :style="{width: variable.width ? `${variable.width}px` : '100px'}">
                <t-link size="small"
                        theme="primary"
                       @click="() => copyText(variable.value)">{{variable.value}}
                </t-link> : {{variable.label}} : {{variable.demo()}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
    <t-input v-bind="formItem.elementProperty"
             v-model:value="modelValue">
    </t-input>
  </t-popup>
</template>

<style scoped lang="less">
.input-format-trigger {
  border-radius: 10px;
  padding: 6px;
  width: 440px;
  background: var(--main-background);
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
  .helper {
    max-height: 190px;
    overflow-y: auto;
  }
}
</style>
