<script setup lang="tsx">
import { useEventListener } from '@vueuse/core';
import { useRoute, useRouter } from 'vue-router'
import { onMounted, ref } from 'vue'
import { useStorageSourceStore, useUserSettingStore } from '@/stores'
import { UtoolsLayout } from '@xiaou66/u-web-ui'
import { routes } from '@/router'
import { DialogPlugin } from 'tdesign-vue-next'

const route = useRoute();
const router = useRouter();
useEventListener(document, 'dragenter', () => {
  if (route.name !== 'uploadFileHome') {
    router.replace({ name: 'uploadFileHome' });
  }
});

const newUser = ref(!utools.dbStorage.getItem('new'));
const storageSourceStore = useStorageSourceStore()
const userSettingStore = useUserSettingStore();

function guideUse() {
  const dialogInstance = DialogPlugin.confirm({
    header: '插件初始化选择',
    closeOnEscKeydown: false,
    closeOnOverlayClick: false,
    body: () => (
      <div>
        <div>「创建默认上传源」将会自动下载「16 图床」存储源插件并创建存储源</div>
        <div>「我要自己探索」 将不会做任何的初始化</div>
        <div style={{ color: '#f15', paddingTop: '10px' }}> 默认图床仅提供测试不保证图片不会丢失</div>
      </div>
    ),
    confirmBtn: '创建默认上传源',
    onConfirm: async () => {
      const pluginCode = '16image';
      if (!window.storagePlugInManager.isInstall(pluginCode)) {
        await window.storagePlugInManager.install(pluginCode);
      }

      const id = storageSourceStore.saveStorageSource({
        id: '',
        storageName: '16图床',
        storagePluginCode: pluginCode,
      });
      const pluginConfig = await window.storagePlugInManager.getPluginConfig(pluginCode);
      if (pluginConfig && pluginConfig.noConfig) {
        const key = `storageSource/${id}`;
        const config: Record<string, any> = {};
        const pluginInstance = window.storagePlugInManager.getPluginInstance(pluginCode);
        pluginInstance.verifyConfig(config);
        utools.dbCryptoStorage.setItem(key, config);
      }

      userSettingStore.currentSelectUploadId = id;
      dialogInstance.hide();
    },
    cancelBtn: '我要自己探索',
    onClose() {
      utools.dbStorage.setItem('new', true);
      newUser.value = false;
      dialogInstance.destroy();
    },
  })
}

onMounted(() => {
  if (newUser.value) {
    guideUse()
  }
});


const useUserCount = ref(0)
onMounted(async () => {
  useUserCount.value = await window.plugInUtils.getPlugUserCount()
});
function handlePlugInHome() {
  utools.shellOpenExternal('utools://图床 Plus')
}

const appName: string = import.meta.env.VITE_NAME;
const avatar = utools.getUser()?.avatar || '/logo.png';
function handleLoadRouter() {
  return routes;
}
</script>

<template>
  <UtoolsLayout :title="appName"
                :avatar="avatar"
                :load-router="handleLoadRouter"/>
<!--  <div class="u-fx">-->
<!--    <LeftMenu />-->
<!--    <div id="content-wrapper">-->
<!--      &lt;!&ndash; 全局 header &ndash;&gt;-->
<!--      <div id="global-header">-->
<!--        <div></div>-->
<!--        <div  v-if="useUserCount"-->
<!--              id="use-user-count"-->
<!--              class="u-fx u-fac u-pointer"-->
<!--              @click="handlePlugInHome">-->
<!--          <iconpark-icon name="fire" style="padding-right: 4px;"></iconpark-icon>-->
<!--          你正在与-->
<!--          <a-tooltip>-->
<!--            <template #content>-->
<!--              <span style="font-size: 12px">数据来自 uTools 插件统计提供</span>-->
<!--            </template>-->
<!--            <span>{{useUserCount}}</span>-->
<!--          </a-tooltip>-->
<!--          位小伙伴一起使用</div>-->
<!--      </div>-->
<!--      <div v-if="!newUser" id="content">-->
<!--        <router-view />-->
<!--      </div>-->
<!--      <div v-if="!newUser" id="global-footer">-->
<!--        <div class="u-fx">-->
<!--          <div> © {{new Date().getFullYear()}} [xiaou]。保留所有权利</div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->

</template>

<style lang="less" scoped>
</style>
