import "tdesign-vue-next/es/style/index.css";
import './assets/less/main.less';
import 'vue-waterfall-plugin-next/dist/style.css'
import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import { useTheme } from '@/hooks/useTheme.ts'
import { StoragePlugInManager } from '@/plugin/StoragePlugin/StoragePlugInManager.ts'
import { usePluginStore } from '@/stores/PluginStore/PluginStore.ts'
import FileUtils from '@/utils/FileUtils.ts'
import { useUserSettingStore } from '@/stores'
import { dispatchUtoolsCodeEvent } from '@xiaou66/u-utools'
import './utoolsEvent.ts';
import { InterconnectService } from '@xiaou66/interconnect-service'

import initLinkService from './LinkServiceEvent.ts';
import PlugInUtils from '@/utils/PlugInUtils.ts'
import {sceneDataTransitionV2} from "@/views/UploadScene/sceneDataTransition.ts";

(() => {
  import('virtual:uno.css')
})()
utools.onPluginDetach(() => {
  document.body.setAttribute('window-type', 'detach')
});

window.plugInUtils = new PlugInUtils({
  plugName: '图床 Plus'
});

// 主题
useTheme({
  setDarkTheme: () => {
    document.documentElement.setAttribute("theme-mode", "dark");
  },
  setDefaultTheme: () => {
    document.documentElement.removeAttribute("theme-mode");
  }
});

window.storagePlugInManager = new StoragePlugInManager();
window.storagePlugInManager.loadAllInstalledPlugin()
  .then(() => {});

const pipDir = window.path.join(utools.getPath('temp'), 'pip');
FileUtils.createDirIfAbsent(pipDir);
window.linkService = new InterconnectService(`picture-bed-plus`,
 window.net, (path) => {
    if (window.fs.existsSync(path)) {
      window.fs.rmSync(path);
    }
  });
initLinkService();

const app = createApp(App)

app.use(createPinia())
app.use(router)

usePluginStore();

// TODO v2.1.0 版本删除
sceneDataTransitionV2();

const userSettingStore = useUserSettingStore();
if (userSettingStore.serviceEnableStatus) {
  window.linkService.start().then(() => {});
}
// 插件自动更新
if (userSettingStore.pluginAutoUpdate) {
  window.storagePlugInManager.updateAllPluginList().then(() => {});
}

app.mount('#app');
window.utools.onPluginEnter((data) => {
  dispatchUtoolsCodeEvent(data, router);
});
