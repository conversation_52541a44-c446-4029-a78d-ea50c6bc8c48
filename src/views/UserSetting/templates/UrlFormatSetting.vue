<script lang="tsx" setup>
import { toRefs } from 'vue'
import { useUserSettingStore } from '@/stores'
import { nanoid } from 'nanoid'
import { CopyUrlFormatSelect } from '@/components/business'
import { MessagePlugin, Input, Link, Button, Icon, type TableProps } from 'tdesign-vue-next'
import { SwitchPlusEnable } from '@xiaou66/u-web-ui'
const { formatUrlData, autoCopyUrlFormat } = toRefs(useUserSettingStore());

// 处理添加格式
function handleAddFormat() {
  formatUrlData.value.push({
    id: nanoid(),
    displayName: '',
    format: '',
    enable: false,
  })
}
// 处理删除格式
function handleDeleteFormat(id: string) {
  const index = formatUrlData.value.findIndex(item => item.id === id);
  if (index > -1) {
    formatUrlData.value.splice(index, 1)
  }
}

function handleFormatChange(value: boolean, index: number) {
  console.log(value, index)
  const { displayName, format } = formatUrlData.value[index]
  if (value && (!displayName || !format)) {
    MessagePlugin.warning('显示名称和链接格式存在未填写无法开启')
    return;
  }
  if (!value &&  autoCopyUrlFormat.value ===  formatUrlData.value[index].id) {
    MessagePlugin.warning('自动复制正在使用无法关闭')
    return;
  }
  formatUrlData.value[index].enable = value
}
const columns: TableProps['columns'] = [
  {
    colKey: 'displayName',
    title: '显示名称',
    width: 42,
    render: (h, { row, type }) => {
      if (type === 'cell') {
        return (
          <Input v-model:value={ row.displayName } />
        )
      }
    }
  },
  {
    colKey: 'format',
    title: '链接格式',
    render: (h, { row, type }) => {
      if (type === 'cell') {
        return (
          <Input v-model:value={ row.format } />
        )
      }
    }
  },
  {
    colKey: 'enable',
    title: '启用',
    width: 25,
    align: 'center',
    render: (h, { row, type, rowIndex }) => {
      if (type === 'cell') {
        return (
          <SwitchPlusEnable
            size={'mini'}
            v-model:value={[ row.enable, (value: boolean) => handleFormatChange(value, rowIndex) ]} />
        )
      }
    }
  },
  {
    colKey: 'operation',
    title: '操作',
    width: 26,
    align: 'center',
    render: (h, { row, type }) => {
      if (type === 'cell') {
        return (
          <Link theme={'danger'}
                size={'small'}
                disabled={ row.disableDelete }
                onClick={() => handleDeleteFormat(row.id)}>
            删除
          </Link>
        )
      } else {
        return (
          <Button size={'small'}
                  theme={'default'}
                  onClick={ handleAddFormat }>
            {{
              icon: () => <Icon class="i-u-plus" />,
              default: () => '添加'
            }}
          </Button>
        )
      }
    }
  },
]
console.log(formatUrlData)
</script>
<template>
 <div>
   <div class="u-fx u-mb6" style="justify-content: flex-end">
     <CopyUrlFormatSelect />
   </div>
   <t-table class="u-web-table"
            :data="formatUrlData"
            :columns="columns"
            row-key="id"
            size="small"
            style="width: 100%"
            :pagination="false"
            :scroll="{ y: 280 }"
            :bordered="false">
   </t-table>
 </div>
</template>
<style lang="less" scoped>

</style>
