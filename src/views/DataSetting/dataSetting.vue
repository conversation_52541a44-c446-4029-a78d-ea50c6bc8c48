<script lang="tsx" setup>
import {toRefs} from "vue";
import { DialogPlugin, Link, NotifyPlugin } from 'tdesign-vue-next'
import {useDataSettingStore} from "@/stores";
import { PageHeader } from '@xiaou66/u-web-ui';
import DataUtils from '@/utils/DataUtils.ts'
const { localAppDataPath } = toRefs(useDataSettingStore());

function handlerOpenLocalAppDataPath() {
  utools.shellShowItemInFolder(localAppDataPath.value);
}


// 删除插件所有数据
function handleDeleteAllPluginData() {
  const dialogInstance = DialogPlugin.confirm({
    header: '二次确认',
    body: '确认要删除插件的目录和插件内所有的数据?',
    confirmBtn: '删除',
    cancelBtn: '取消',
    theme: 'warning',
    onConfirm() {
      utools.db.allDocs()
        .map(({_id}) => utools.dbStorage.removeItem(_id))
      const tempsPath = DataUtils.getDataSavePath();
      window.fs.rmSync(tempsPath, {
        recursive: true,
        force: true
      });
      utools.getFeatures()
        .map(({ code }) => utools.removeFeature(code));
      NotifyPlugin.success({
        title: '提示',
        content: '清理成功, 需要完全退出插件后再进入即可'
      });
      dialogInstance.destroy();
      utools.outPlugin(true);
    },
  })
}

function handleTransferLocalAppDataPath() {
  const dialogInstance = DialogPlugin.alert({
    header: '迁移数据目录提示',
    body: '选择目前不能是当前目录子目录',
    confirmBtn: '我知道了',
    onConfirm: () => {
      dialogInstance.hide();
      dialogInstance.destroy();
      handleTransferLocalAppDataPathInner();
    },
  })
}
function handleTransferLocalAppDataPathInner() {
  const newPaths = utools.showOpenDialog({
    title: '请选择迁移到目录',
    properties: ['createDirectory', 'openDirectory']
  });

  if (!newPaths || !newPaths.length) {
    return;
  }

  const newPath = newPaths[0];
  if (newPath === localAppDataPath.value) {
    return;
  }
  if (newPath.startsWith(localAppDataPath.value)) {
    NotifyPlugin.warning({
      title: "提示",
      content: "新目录不能是之前目录的子目录",
      duration: 3000
    });
    return;
  }
  const dialogInstance = DialogPlugin.confirm({
    header: '迁移确认',
    confirmBtn: '确认',
    closeOnEscKeydown: false,
    closeOnOverlayClick: false,
    body: () => (
      <div>
        <div>请手动迁移数据, 插件内不再迁移数据功能, 将当前数据文件夹数据移动到要迁移目录下</div>
        <div class="flex gap-2">
          <div>当前目录:</div>
          <Link size="small"
                theme="primary"
                prefixIcon={() => <div class="w-4 h-4 i-u-folder-open" />}
                onClick={() => utools.shellOpenPath(newPath)} >
            打开
          </Link>
        </div>
        <div class="break-all whitespace-pre-wrap">{ localAppDataPath.value }</div>
        <div class="flex gap-2">
          <div>迁移目录:</div>
          <Link size="small"
                theme="primary"
                prefixIcon={() => <div class="w-4 h-4 i-u-folder-open" />}
                onClick={() => utools.shellOpenPath(newPath)} >
            打开
          </Link>
        </div>
        <div class="break-all whitespace-pre-wrap">{ newPath }</div>
      </div>
    ),
    onConfirm: () => {
      localAppDataPath.value = newPath;
      dialogInstance.destroy();
    }
  })
}
</script>
<template>
  <div class="u-main-content-no-padding">
    <PageHeader title="数据管理"
                size="small"
                subtitle="这里是可以管理插件数据, 如果需要管理插件数据均在这里配置" />
    <div class="u-main-content-inner p-3">
      <div class="u-gap10 u-radius10"
           style="padding: 14px; flex-direction: column; background: var(--color-bg-3);">
        <div class="u-fx u-fac u-f-between u-mb12">
          <div class="u-bold">插件数据目录</div>
          <div class="u-fx u-fac u-gap10">
            <t-link size="small"
                    theme="primary"
                    @click="handleTransferLocalAppDataPath">
              <template #prefixIcon>
                <div class="w-4 h-4 i-u-switch" />
              </template>
              迁移位置
            </t-link>
            <t-link size="small"
                    theme="primary"
                    @click="handlerOpenLocalAppDataPath">
              <template #prefixIcon>
                <div class="w-4 h-4 i-u-folder-open" />
              </template>
              打开
            </t-link>
          </div>
        </div>
        <div>
          <t-input :model-value="localAppDataPath"
                   readonly />
        </div>
        <div class="u-tips u-mt10">存储插件额外需要支持库及运行时部分缓存, 无需清理插件会定期清理</div>
      </div>
      <div class="u-gap10 u-radius10 u-mt10"
           style="padding: 14px; flex-direction: column; background: var(--color-bg-3);">
        <div class="u-fx u-fac u-f-between u-mb12">
          <div class="u-bold">数据清理</div>
          <div class="flex gap-3">
            <t-button size="small"
                      status="danger"
                      theme="danger"
                      @click="handleDeleteAllPluginData">
              <template #icon>
                <div class="w-4 h-4 i-u-delete" style="margin-right: 4px;"></div>
              </template>
              清理插件内数据
            </t-button>
          </div>
        </div>
        <div class="u-tips u-mt10">数据清理操作不可恢复，请谨慎操作</div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.tips {
  margin-bottom: 16px;
}
</style>
