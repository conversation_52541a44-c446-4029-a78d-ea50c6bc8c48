<script setup lang="ts">
import type { StorageSourceSaveDrawerInstance } from './StorageSourceSaveDrawer.ts'
import { type StorageSourceItem, useStorageSourceStore } from '@/stores'
import { onMounted, ref, toRaw, useTemplateRef, watch, defineAsyncComponent } from 'vue'
import type { StoragePlugInConfig } from '@xiaou66/picture-plugin';
import { MessagePlugin, type FormInstanceFunctions } from 'tdesign-vue-next'
import { InputFileFormat } from '../../../components/plugin/InputFormat'
import { useMultiLoading, useSingleLoading } from '@/hooks/useLoading.ts'
import { RadioGroup } from '../../../components/plugin/RadioGroup'
import { InputButton } from '../../../components/plugin/Button'

const props = defineProps<{
  storageId: string;
  storageSourceRef: StorageSourceSaveDrawerInstance,
}>();

const storageSourceStore = useStorageSourceStore();

const storageSource = ref<StorageSourceItem>();
const pluginConfig = ref<StoragePlugInConfig>();
async function loadStorageSource() {
  if (props.storageId) {
    storageSource.value = storageSourceStore.getStorageSourceById(props.storageId);
    pluginConfig.value = await window.storagePlugInManager
      .getPluginConfig(storageSource.value.storagePluginCode);
    const key = `storageSource/${props.storageId}`;
    console.log(' pluginConfig.value',  pluginConfig.value);
    form.value = utools.dbCryptoStorage.getItem(key) || {};
  }
}

watch(() => props.storageId, () => {
  loadStorageSource().then(() => {});
});

onMounted(() => {
  loadStorageSource().then(() => {});
});

function reloadStorageConfig() {
  loadStorageSource();
}
const form = ref<Record<string, any>>({});
const formRef = useTemplateRef<FormInstanceFunctions>('formRef');

const [handleSaveConfig, saveLoading] = useSingleLoading(async () => {
  const error = await formRef.value?.validate();
  const key = `storageSource/${props.storageId}`;
  if (error !== true) {
    return;
  }
  const storageSource = storageSourceStore.getStorageSourceById(props.storageId);
  const pluginInstance = window.storagePlugInManager.getPluginInstance(storageSource.storagePluginCode);
  debugger;
  try {
    await pluginInstance.verifyConfig(form.value)
  }catch (error: any) {
    return;
  }
  utools.dbCryptoStorage.setItem(key, toRaw(form.value));
  await MessagePlugin.success('保存成功');
});

function handleCopyText() {
  utools.copyText(props.storageId);
  MessagePlugin.success("复制成功");
}
const isDev = utools.isDev();

const [handleInstallPlugin, installStatus] = useMultiLoading(async (pluginCode: string) => {
  await window.storagePlugInManager.install(pluginCode);
  await loadStorageSource();
}, (pluginCode) => pluginCode);
</script>

<template>
  <div class="u-pos-rel config-container-wrapper">
    <div v-if="storageId" class="config-container">
      <div class="u-fx u-f-between u-mb10">
        <div></div>
        <div class="u-fx u-gap10">
          <t-tooltip content="本地服务使用">
            <t-button class="u-web-button"
                      theme="default"
                      @click="handleCopyText()">
              复制 ID
            </t-button>
          </t-tooltip>
          <t-button v-if="isDev"
                    theme="default"
                    @click="reloadStorageConfig">
            重新加载配置
          </t-button>
          <t-button theme="default"
                    :loading="saveLoading"
                    @click="handleSaveConfig">
            <template #icon>
              <t-icon class="i-u-save-one" />
            </template>
            保存
          </t-button>
        </div>
      </div>
      <div>
        <div v-if="pluginConfig && pluginConfig.uiConfig">
          <div v-if="pluginConfig.uiConfig.tips"
               class="u-mb10">
            <t-alert title="提示"
                     size="small"
                     style="background-color: rgba(var(--blue-10)); padding: 10px;">
              <div v-html="pluginConfig.uiConfig.tips"></div>
            </t-alert>
          </div>
          <t-form ref="formRef"
                  v-model:data="form"
                  auto-label-width>
            <t-form-item v-for="(formItem, index) in pluginConfig.uiConfig.forms"
                         :key="index" v-bind="formItem.formItem">
              <t-input v-if="formItem.type === 'input'"
                       v-bind="formItem.elementProperty"
                       v-model:value="form[formItem.formItem.name]" />
              <InputFileFormat v-else-if="formItem.type === 'input-format'"
                               :form-item="formItem"
                               v-model:model-value="form[formItem.formItem.name]" />
              <RadioGroup v-else-if="formItem.type === 'radio-group'"
                          :form-item="formItem"
                          v-model:model-value="form[formItem.formItem.name]" />
              <InputButton v-else-if="formItem.type === 'input-button'"
                           :form-item="formItem"
                           v-model:model-value="form" />
              <component v-else-if="formItem.type === 'custom' && formItem.customComponent"
                         :is="defineAsyncComponent(formItem.customComponent)"
                         :form-item="formItem"
                         v-model:value="form[formItem.formItem.name]" />
            </t-form-item>
          </t-form>
        </div>
        <div v-else-if="storageSource" class="pt-10">
          <t-empty>
            <template #title>
              <div class="u-mb10">
                缺少 {{storageSource.storagePluginCode}} 插件无法使用
              </div>
              <t-button
                :loading="installStatus(storageSource!.storagePluginCode)"
                @click="() => handleInstallPlugin(storageSource!.storagePluginCode)"
                variant="outline"
                theme="primary">
                点击下载
              </t-button>
            </template>
          </t-empty>
        </div>
      </div>
    </div>
    <div v-else>
      <t-empty style="padding-top: 100px;">
        <template #title>
          <div class="u-mb10">暂时无上传源</div>
          <div>
            <t-button theme="primary"
                      variant="outline"
                      @click="() => storageSourceRef!.show()">
              添加存储源
            </t-button>
          </div>
        </template>
      </t-empty>
    </div>
  </div>
</template>

<style scoped lang="less">
.config-container-wrapper {
  width: 100%;
  height: 100%;
  .config-container {
    position: absolute;
    width: 88%;
    left: 50%;
    padding: 10px;
    border-radius: 10px;
    transform: translateX(-50%);
  }
}
.empty-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
:deep(.arco-alert-title) {
  font-size: 14px;
}
</style>
