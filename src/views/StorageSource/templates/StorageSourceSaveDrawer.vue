<script setup lang="ts">
import { computed, ref, useTemplateRef } from 'vue'
import type { StorageSourceSaveDrawerInstance } from './StorageSourceSaveDrawer.ts'
import { type StorageSourceItem, useStorageSourceStore } from '@/stores'
import type { FormInstanceFunctions } from 'tdesign-vue-next'
import {  cloneDeep } from 'es-toolkit'
import SysPluginApi, { type PluginListGetResponse } from '@/api/ucloud/SysPluginApi.ts'
import type { PlugInfo } from '@xiaou66/picture-plugin';
import { useSingleLoading } from '@/hooks/useLoading.ts'
import { Scrollbar } from '@xiaou66/u-web-ui'
const visible = ref(false)


const defaultFormValue = {
  id: '',
  storageName: '',
  storagePluginCode: '',
}
const form = ref<{
  id: string
  storageName: string
  storagePluginCode: string
}>(cloneDeep(defaultFormValue));
const installedUploadSourceList = ref<PlugInfo[]>([]);
const onlineUploadSourceList = ref<PluginListGetResponse>({
  total: 0,
  list: []
})
const onlyInstalled = ref<boolean>(false);
const listData = computed(() => {
  if (onlyInstalled.value) {
    return installedUploadSourceList.value;
  } else {
    return onlineUploadSourceList.value.list;
  }
});

const uploadSourceChoice = ref({
  uploadSourceType: '',
});
async function requestList() {
  onlineUploadSourceList.value = await SysPluginApi.pluginListGetApi({
    pageNo: 1,
    pageSize: -1,
    pluginGroup: uploadSourceChoice.value.uploadSourceType
  })
}
function show(storageSource: StorageSourceItem) {
  window.storagePlugInManager.getAllPluginConfig().then((res) => {
      installedUploadSourceList.value = res.map(item => item.pluginInfo);
      requestList();
      form.value = cloneDeep(defaultFormValue);
      if (storageSource && storageSource.id) {
        form.value = {
          ...form.value,
          ...cloneDeep(storageSource),
        };
      }
    visible.value = true
  })
}

defineExpose<StorageSourceSaveDrawerInstance>({
  show,
});


const formRef = useTemplateRef<FormInstanceFunctions>('formRef');

const [handleVerifyFormLoading, saveStatus] = useSingleLoading(handleVerifyForm);
async function handleVerifyForm() {
  const error = await formRef.value!.validate();
  if (error !== true) {
    return false;
  }

  if (!window.storagePlugInManager.isInstall(form.value.storagePluginCode)) {
    await window.storagePlugInManager.install(form.value.storagePluginCode);
  }

  await handleSaveSource();
}
const storageSourceStore = useStorageSourceStore()

const emits = defineEmits<{
  saveOk: [id: string],
}>()
async function handleSaveSource() {
  const pluginConfig = await window.storagePlugInManager.getPluginConfig(form.value.storagePluginCode);
  if (!pluginConfig) {
    return;
  }
  const id = storageSourceStore.saveStorageSource({
    ...form.value,
  });

  if (pluginConfig.noConfig) {
    const key = `storageSource/${id}`;
    utools.dbCryptoStorage.setItem(key, {});
  }

  visible.value = false;
  emits('saveOk', id);
}
</script>

<template>
  <t-drawer class="uploadSourceSaveDrawer"
            size="90%"
            header="保存上传源"
            v-model:visible="visible"
            destroyOnClose
            @confirm="handleVerifyForm"
            lazy>
    <template #footer>
      <div class="flex justify-center">
        <t-button @click="visible = false" theme="default">放弃</t-button>
        <t-button :loading="saveStatus"
                  @click="handleVerifyFormLoading">保存</t-button>
      </div>
    </template>
    <t-form ref="formRef"
            v-model:data="form">
      <t-form-item
        label="图源名称"
        name="storageName"
        :rules="[{ required: true, message: '图源名称请取个好听的名称' }]"
      >
        <t-input v-model:value="form.storageName" />
      </t-form-item>
      <t-form-item
        :validate-trigger="'input'"
        label="图源选择"
        name="storagePluginCode"
        :rules="[{ required: true, message: '请选择一个图源' }]"
      >
        <div class="u-fx u-f-between u-fac" style="width: 100%">
          <div>
            <t-radio-group
              v-if="!onlyInstalled"
              class="u-mb10 u-web-radio-group"
              v-model:model-value="uploadSourceChoice.uploadSourceType"
              variant="default-filled"
              @change="requestList"
            >
              <t-radio-button value="">全部</t-radio-button>
              <t-radio-button value="cloudVendor">云厂商</t-radio-button>
              <t-radio-button value="self">自建</t-radio-button>
              <t-radio-button value="other">其他</t-radio-button>
            </t-radio-group>
            <div class="u-font-size-smail u-fx u-fac u-gap5 pt-2">
              <t-checkbox v-model:model-value="onlyInstalled"
                          @change="() => form.storagePluginCode = ''" >
                <span class="text-sm">仅查看已安装</span>
              </t-checkbox>
            </div>
          </div>
          <div>
<!--            <a-input-search size="small"></a-input-search>-->
          </div>
        </div>
        <template #help>
          <div class="u-fx u-f-between u-fac" style="width: 100%">
            <div>有些上传源都需要额外下载, 因为保证插件所占内存皆为用户所需</div>
          </div>
          <Scrollbar style="height: calc(100vh - 262px);">
            <div class="source-type-list">
              <div
                v-for="(item) in listData"
                :key="item.pluginCode"
                class="source-type-item"
                :class="{ active: form.storagePluginCode === item.pluginCode }"
                @click="() => (form.storagePluginCode = item.pluginCode!)"
              >
                <div>
                  <div class="u-fx u-fac u-gap10">
                    <img style="width: 32px; height: auto"
                         :src="item.pluginLogo"
                         alt="" />
                    <div class="title">{{ item.pluginName }}</div>
                  </div>
                  <div class="content">
                    <div class="desc">{{ item.pluginDesc }}</div>
                  </div>
                </div>
              </div>
            </div>
          </Scrollbar>
        </template>
      </t-form-item>
    </t-form>
  </t-drawer>
</template>
<style lang="less">
.uploadSourceSaveDrawer {
  .arco-drawer-body {
    padding: 12px 12px 0px;
  }
}
</style>
<style scoped lang="less">
.source-type-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  padding-top: 10px;
  width: 100%;
}

.source-type-item {
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  width: 280px;
  &:hover,
  &.active {
    border-color: #165dff;
    background-color: rgba(22, 93, 255, 0.05);
  }

  .icon {
    font-size: 24px;
    color: #165dff;
  }
  .title {
    color: var(--text-color);
    font-weight: 500;
  }

  .content {
    .desc {
      color: var(--text-tips-color);
      font-size: 12px;
      padding: 4px 0;
    }
  }
}
:deep(.arco-form-item-message-help) {
  padding-top: 4px;
  width: 100%;
}
</style>
