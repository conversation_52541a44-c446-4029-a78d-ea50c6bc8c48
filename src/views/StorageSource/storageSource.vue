<script setup lang="ts">
import UploadSourceSaveDrawer from './templates/StorageSourceSaveDrawer.vue';
import { onMounted, ref, useTemplateRef, watch } from 'vue'
import { type StorageSourceItem, useStorageSourceStore } from '@/stores'
import StorageSourceDetail from './templates/StorageSourceDetail.vue'
import type {
  StorageSourceSaveDrawerInstance,
} from './templates/StorageSourceSaveDrawer.ts'
import { Scrollbar, SplitPanel } from '@xiaou66/u-web-ui';
import { DialogPlugin } from 'tdesign-vue-next';


const props = defineProps<{
  mode?: 'add'
}>();

const storageSourceRef = useTemplateRef<StorageSourceSaveDrawerInstance>('storageSourceRef');

const storageSourceStore = useStorageSourceStore();

const activeId = ref();
function setActiveId(id: string) {
  activeId.value = id;
}

function handleDeleteStorageSource(storageSource: StorageSourceItem) {
  const dialogInstance = DialogPlugin.confirm({
    header: '二次确认',
    body: `是否删除「${storageSource.storageName}」存储源, 删除后无法还原`,
    confirmBtn: '删除',
    cancelBtn: '取消',
    theme: 'warning',
    onConfirm: async () => {
      storageSourceStore.deleteStorageSource(storageSource.id);
      dialogInstance.hide();
      dialogInstance.destroy();
      return true;
    }
  });
}
onMounted(() => {
  if (storageSourceStore.storageSourceList.length > 0) {
    activeId.value = storageSourceStore.storageSourceList[0].id
  }
});

watch(() => props.mode, () => {
  if (props.mode === 'add') {
    storageSourceRef.value?.show();
  }
});
onMounted(() => {
  if (props.mode === 'add') {
    storageSourceRef.value?.show();
  }
});
</script>

<template>
  <div class="u-main-content-no-padding">
    <UploadSourceSaveDrawer ref="storageSourceRef"
                            @saveOk="(id) => setActiveId(id)">
    </UploadSourceSaveDrawer>
    <SplitPanel default-size="160"
                min="160">
      <template #first>
        <div class="menu-sub-container">
          <div class="menu-sub-header">
            <div class="title">上传源</div>
            <div>
              <t-button size="small"
                        theme="default"
                        class="u-web-transparent"
                        @click="() => storageSourceRef!.show()">
                <template #icon>
                  <t-icon class="i-u-plus"></t-icon>
                </template>
                添加
              </t-button>
            </div>
          </div>
          <Scrollbar style="height: 100%;">
            <div class="menu-sub">
              <div
                v-for="item in storageSourceStore.storageSourceList"
                :key="item.id"
                class="u-fx u-fac u-gap10 menu-item"
                style="justify-content: space-between"
                :class="{ active: activeId === item.id}"
                @click="() => setActiveId(item.id)">
                <div class="u-fx u-fac u-gap10" style="font-size: 13px">
                  <div>{{ item.storageName }}</div>
                </div>
                <div>
                  <t-dropdown size="small" placement="bottom">
                    <div class="i-u-more-one"></div>
                    <t-dropdown-menu>
                      <t-dropdown-item @click="() => storageSourceRef?.show(item)">
                        <template #prefixIcon>
                          <div class="i-u-write"></div>
                        </template>
                        编辑
                      </t-dropdown-item>
                      <t-dropdown-item theme="error"
                                       @click="() => handleDeleteStorageSource(item)">
                        <template #prefixIcon>
                          <div class="i-u-delete"></div>
                        </template>
                        删除
                      </t-dropdown-item>
                    </t-dropdown-menu>
                  </t-dropdown>
                </div>
              </div>
            </div>
          </Scrollbar>
        </div>
      </template>
      <template #second>
        <StorageSourceDetail v-if="activeId"
                             :storage-id="activeId"
                             :storageSourceRef="storageSourceRef!">
        </StorageSourceDetail>
      </template>
    </SplitPanel>
  </div>
</template>
