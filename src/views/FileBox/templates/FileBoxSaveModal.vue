<script setup lang="ts">
import {nextTick, ref, useTemplateRef} from 'vue'
import type { FileBoxSaveModalInstance } from './FileBoxSaveModal.ts'
import { type FileBoxInfoItem, useFileBoxStore } from '@/stores'
import { cloneDeep } from 'es-toolkit'
import type { FormInstanceFunctions } from 'tdesign-vue-next'

const visible = ref(false);
const defaultFormValue: FileBoxInfoItem = {
  id: '',
  fileBoxName: '',
};
const form = ref(cloneDeep(defaultFormValue));
function show(fileBoxInfo?: FileBoxInfoItem) {
  form.value = cloneDeep(defaultFormValue);
  if (fileBoxInfo) {
    form.value = {
      ...form.value,
      ...fileBoxInfo,
    }
  }
  visible.value = true;
  nextTick(() => {
    fileBoxNameInputRef.value?.focus()
  })
}
defineExpose<FileBoxSaveModalInstance>({
  show,
});
const formRef = useTemplateRef<FormInstanceFunctions>('formRef')
async function handleVerifyForm() {
  const res = await formRef.value?.validate();
  console.log('res', res)
  if (res !== true) {
    return false;
  }
  const id = fileBoxStore.saveFileBoxInfo(form.value)
  emits('saveOk', id);
  visible.value = false;
}
const fileBoxStore = useFileBoxStore();

const emits = defineEmits<{
  saveOk: [id: string];
}>()

const fileBoxNameInputRef = useTemplateRef<HTMLInputElement>('fileBoxNameInputRef');
</script>

<template>
  <t-dialog v-model:visible="visible"
            header="保存文件盒子"
            confirm-btn="保存"
           @confirm="handleVerifyForm">
    <t-form v-model:data="form"
            ref="formRef">
      <t-form-item
        label="盒子名称"
        name="fileBoxName"
        :rules="[{ required: true, message: '请取一个好听盒子名称' }]">
        <t-input ref="fileBoxNameInputRef"
                 v-model:value="form.fileBoxName"
                 @pressEnter="handleVerifyForm" />
      </t-form-item>
      <t-form-item style="display: none"
                   :status-icon="false" />
    </t-form>
  </t-dialog>
</template>

<style scoped lang="less">

</style>
