<script setup lang="ts">
import { type FileBoxInfoItem, useFileBoxStore } from '@/stores'
import { onMounted, ref, useTemplateRef } from 'vue'
import FileBoxSaveModal from './templates/FileBoxSaveModal.vue'
import type { FileBoxSaveModalInstance } from './templates/FileBoxSaveModal.ts'
import FileBoxDetail from '@/views/FileBox/templates/FileBoxDetail.vue'
import { Empty, DialogPlugin } from 'tdesign-vue-next';
import { Scrollbar, SplitPanel } from '@xiaou66/u-web-ui';
const fileBoxStore = useFileBoxStore();
const activeId = ref();
function setActiveId(id: string) {
  activeId.value = id;
}
onMounted(() => {
  if (fileBoxStore.fileBoxInfoList.length > 0) {
    activeId.value = fileBoxStore.fileBoxInfoList[0].id;
  }
})
const fileBoxSaveRef = useTemplateRef<FileBoxSaveModalInstance>('fileBoxSaveRef');
function handleDeleteFileBox(item: FileBoxInfoItem) {
  const dialogInstance = DialogPlugin.confirm({
    closeOnEscKeydown: false,
    closeBtn: false,
    header: '二次确认',
    body: `是否删除「${item.fileBoxName}」盒子`,
    onConfirm: async () => {
      await fileBoxStore.removeFileBox(item.id);
      if (fileBoxStore.fileBoxInfoList.length > 0) {
        activeId.value = fileBoxStore.fileBoxInfoList[0].id;
      }
      dialogInstance.hide();
      dialogInstance.destroy();
      return true;
    }
  })
}
</script>
<template>
  <FileBoxSaveModal ref="fileBoxSaveRef" @saveOk="(id) => setActiveId(id)" />
  <div class="u-main-content-no-padding">
    <SplitPanel default-size="160"
                min="160">
      <template #first>
        <div class="menu-sub-container">
          <div class="menu-sub-header">
            <div class="title">文件盒子</div>
            <div>
              <t-button style="width: 100%;"
                        size="small"
                        theme="default"
                        @click="fileBoxSaveRef?.show()">
                <template #icon>
                  <t-icon class="i-u-plus"></t-icon>
                </template>
                添加
              </t-button>
            </div>
          </div>
          <Scrollbar style="height: 100%;">
            <div class="menu-sub">
              <div v-for="item in fileBoxStore.fileBoxInfoList"
                   :key="item.id"
                   class="u-fx u-fac u-gap10 menu-item"
                   style="justify-content: space-between"
                   :class="{ active: activeId === item.id}"
                   @click="() => setActiveId(item.id)">
                <div class="u-fx u-fac u-gap10" style="font-size: 13px">
                  <div>{{ item.fileBoxName }}</div>
                </div>
                <div>
                  <t-dropdown size="small"
                              placement="bottom"
                              trigger="hover">
                    <div class="i-u-more-one"></div>
                    <t-dropdown-menu>
                      <t-dropdown-item @click="() => fileBoxSaveRef?.show(item)">
                        <template #prefixIcon>
                          <div class="i-u-write"></div>
                        </template>
                        编辑
                      </t-dropdown-item>
                      <t-dropdown-item theme="error"
                                       @click="() => handleDeleteFileBox(item)">
                        <template #prefixIcon>
                          <div class="i-u-delete"></div>
                        </template>
                        删除
                      </t-dropdown-item>
                    </t-dropdown-menu>
                  </t-dropdown>
                </div>
              </div>
            </div>
          </Scrollbar>
        </div>
      </template>
      <template #second>
        <FileBoxDetail v-if="activeId"
                       :file-box-id="activeId" />
        <div v-else>
          <Empty>
          </Empty>
        </div>
      </template>
    </SplitPanel>
  </div>
</template>
