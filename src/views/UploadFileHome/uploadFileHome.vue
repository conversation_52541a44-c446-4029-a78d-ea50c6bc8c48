<script lang="ts" setup>
import { FileUpload } from '@/components/FileUpload'
import type { FileUpdateInfoItem, FileUploadItem } from '@/@types'
import { computed, toRefs, useTemplateRef } from 'vue'
import { useEventListener } from '@vueuse/core'
import FileUtils from '@/utils/FileUtils.ts';
import { useStorageSourceStore, useUploadFileListStore, useUserSettingStore, useSceneStore } from '@/stores'
import { MessagePlugin, type CascaderProps } from 'tdesign-vue-next'
import { FileUploadImageItem, FileUploadingItem, FileUploadLineItem } from '@/components/FileItem'
import FileIconPreview from '@/components/FilePreview/FileIconPreview.vue'
import { Waterfall } from 'vue-waterfall-plugin-next'
import { CopyUrlFormatSelect } from '@/components/business'
import { Scrollbar, LazyLoader } from '@xiaou66/u-web-ui'
import type { ScrollbarInstance } from '@xiaou66/u-web-ui'
const storageSourceStore = useStorageSourceStore();
const { currentSelectUploadId, uploadPageViewMode } = toRefs(useUserSettingStore());
const uploadFileListStore = useUploadFileListStore();
function fileAdd(fileList: FileUploadItem[]) {
  if (!currentSelectUploadId.value) {
    MessagePlugin.warning("选择上传方式");
  }
  console.log('fileAdded', fileList);
  uploadFileListStore.addUploadFileItemByDefault(...fileList);
}

const mainContentRef = useTemplateRef<HTMLDivElement>('mainContentRef');
const scrollbarRef = useTemplateRef<ScrollbarInstance>('scrollbarRef');

// const router = useRouter();
// function toRouterAddStoreSource() {
//   router.replace({
//     name: 'StorageSource',
//     query: {
//       mode: 'add',
//     }
//   });
// }

// 当用户拖拽文件进入自动回到顶部
useEventListener(mainContentRef, 'dragenter', (e) => {
  console.log('mainContentRef', e);
  if (scrollbarRef.value) {
    scrollbarRef.value.scrollTop(0);
  }
});
const sceneStore = useSceneStore();

const selectUploadSourceOptions = computed<CascaderProps['options']>(() => {
  const storageSourceList = storageSourceStore.storageSourceList;
  const sceneInfoList = sceneStore.sceneInfoList;
  return [
    {
      label: '上传场景',
      value: 'uploadScene',
      children: sceneInfoList
        .map(item => ({ label: item.sceneName, value: item.id})),
    },
    {
      label: '存储源',
      value: 'storageSource',
      children: storageSourceList
        .filter(item => utools.dbCryptoStorage.getItem(`storageSource/${item.id}`))
        .map(item => ({label: item.storageName, value: item.id})),
    }
  ] as CascaderProps['options'];
});

// 重新上传
function handleReloadUpload(uploadFileItem: FileUpdateInfoItem) {
  uploadFileListStore.reloadUploadFileItem(uploadFileItem)
}
function handleRemoveUploadItem(uploadFileItem: FileUpdateInfoItem) {
  uploadFileListStore.removeUploadItem(uploadFileItem);
  MessagePlugin.success("移除成功");
}
</script>

<template>
  <div class="u-main-content-no-padding"
       style="overflow:hidden"
       ref="mainContentRef">
    <Scrollbar ref="scrollbarRef"
                 style="height: 100%;">
      <div>
        <FileUpload @add="fileAdd"></FileUpload>
        <div class="u-fx u-f-between upload-controller">
          <div class="u-fx u-gap5">
            <t-cascader :options="selectUploadSourceOptions"
                        v-model:value="currentSelectUploadId"
                        :style="{ width:'220px' }"
                        placeholder="选择上传方式"
                        size="small"
                        filterable>
            </t-cascader>
            <t-tooltip  content="自动复制上传文件后地址格式, 批量上传不建议使用">
              <CopyUrlFormatSelect />
            </t-tooltip>
          </div>
          <div class="u-fx u-fac u-gap10">
            <t-tooltip  position="left">
              <template #content>
                <span class="u-font-size-smail">超过100个会将最早上传的移除</span>
              </template>
              <div class="u-font-size-smail u-pointer">
                当前上传记录最大值为 100 个
              </div>
            </t-tooltip>
            <t-radio-group v-model:value="uploadPageViewMode"
                           size="small"
                           variant="default-filled">
              <t-tooltip content="列表">
                <t-radio-button value="list">
                  <div class="i-u-view-grid-list" />
                </t-radio-button>
              </t-tooltip>
              <t-tooltip content="大图">
                <t-radio-button value="image">
                  <div class="i-u-picture" />
                </t-radio-button>
              </t-tooltip>
            </t-radio-group>
          </div>
        </div>
        <div v-if="uploadPageViewMode === 'list'" class="upload-file-container">
          <!--   待上传   -->
          <div v-for="(fileItem) in uploadFileListStore.uploadFileList"
               :key="fileItem.id">
            <FileUploadingItem :data="fileItem"
                               :select-upload-source-options="selectUploadSourceOptions"></FileUploadingItem>
          </div>
          <!--   历史上传   -->
          <div v-for="(fileLibrary) in uploadFileListStore.uploadFileHistoryList"
               :key="fileLibrary.id">
            <div style="min-height: 50px">
              <div>
                <LazyLoader :h="'68px'">
                  <FileUploadLineItem :file-library="fileLibrary" />
                </LazyLoader>
              </div>
            </div>
          </div>
        </div>
        <div v-else-if="uploadPageViewMode === 'image'"
             class="upload-file-container">
          <!--   待上传   -->
          <div v-for="(fileItem) in uploadFileListStore.uploadFileList"
               :key="fileItem.id"
               class="u-fx u-f-between u-fac upload-file-item">
            <div class="file-info">
              <div class="file-preview">
                <FileIconPreview :content="fileItem.filePath ? `file://${fileItem.filePath}` : fileItem.content!"
                                 :file-suffix="fileItem.fileSuffix" />
              </div>
              <div class="file-details">
                <div class="file-name">{{ fileItem.fileName }}</div>
                <div class="file-size" v-if="fileItem.fileSize">
                  {{ FileUtils.formatFileSize(fileItem.fileSize) }}
                </div>
              </div>
            </div>
            <div>
              <t-tag v-if="fileItem.uploadStatus === 'waiting'"
                     style="border-radius: 10px">等待上传</t-tag>
              <t-tag v-else-if="fileItem.uploadStatus === 'uploading'"
                     style="border-radius: 10px"
                     color="green"
                     loading>
                上传中
              </t-tag>
              <t-tag v-else-if="fileItem.uploadStatus === 'failed'"
                     style="border-radius: 10px"
                     color="red">
                上传失败
              </t-tag>
            </div>
            <div class="u-fx u-gap5" style="flex-direction: column; align-items: end;">
              <t-cascader v-if="fileItem.uploadStatus === 'failed'"
                          :options="selectUploadSourceOptions"
                          v-model:value="fileItem.uploadWay"
                          :style="{width:'150px'}"
                          placeholder="选择上传方式"
                          size="small"
                          filterable>
              </t-cascader>
              <div v-if="fileItem.uploadStatus === 'failed'"
                   class="u-font-size-smail u-fx u-gap10">
                <t-link theme="danger"
                        size="small"
                        @click="() => handleRemoveUploadItem(fileItem)">移除</t-link>
                <t-link theme="primary"
                        size="small"
                        @click="() => handleReloadUpload(fileItem)">
                  重新上传
                </t-link>
              </div>
            </div>
          </div>
          <!--   历史上传   -->
          <Waterfall :list="uploadFileListStore.uploadFileHistoryList">
            <template #item="{ item: fileLibrary }">
              <div>
                <FileUploadImageItem :fileLibrary="fileLibrary" />
              </div>
            </template>
          </Waterfall>
        </div>
      </div>
    </Scrollbar>

  </div>
</template>

<style lang="less" scoped>
:deep(.arco-radio-button-content) {
  padding: 0px 8px;
}
.upload-controller {
  padding: 0 10px;
}

.upload-file-container {
  padding: 10px;
}
.upload-file-item {
  padding: 12px;
  margin-bottom: 8px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  transition: all 0.3s ease;
  color: var(--text-color);
  background-color: var(--u-bg-color-3);
  &:hover {
    box-shadow: var(--u-shadow-sm);
  }
}
</style>
