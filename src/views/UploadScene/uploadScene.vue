<script setup lang="ts">
import UploadSceneSaveModal from './templates/UploadSceneSaveModal.vue'
import { onMounted, ref, useTemplateRef } from 'vue'
import type { UploadSceneSaveModalInstance } from './templates/UploadSceneSaveModal.ts';
import { type SceneInfoItem, useSceneStore } from '@/stores'
import { MessagePlugin, DialogPlugin, Empty } from 'tdesign-vue-next';
import { Scrollbar, SplitPanel } from '@xiaou66/u-web-ui';
import UploadSceneFlowDetail from './templates/UploadSceneFlowDetail.vue'

const uploadSceneSaveModalRef = useTemplateRef<UploadSceneSaveModalInstance>('uploadSceneSaveModalRef');

const sceneStore = useSceneStore();
const activeId = ref();
function setActiveId(id: string) {
  activeId.value = id;
}
onMounted(() => {
  if (sceneStore.sceneInfoList.length) {
    activeId.value = sceneStore.sceneInfoList[0].id;
  }
});

function handleDeleteScene(sceneInfo: SceneInfoItem) {
  const sceneStore = useSceneStore();
  const dialogInstance = DialogPlugin.confirm({
    closeOnEscKeydown: false,
    closeBtn: false,
    header: '二次确认',
    body: `是否删除「${sceneInfo.sceneName}」场景`,
    onConfirm: async () => {
      sceneStore.removeScene(sceneInfo.id);
      if (sceneStore.sceneInfoList.length) {
        activeId.value = sceneStore.sceneInfoList[0].id;
      }
      dialogInstance.hide();
      dialogInstance.destroy();
      return true;
    }
  });
}
function handleAddScene() {
  if (sceneStore.sceneInfoList.length >= 6) {
    MessagePlugin.info("你已经创建了 6 个场景了. 目前仅支持 6 个场景");
    return;
  }
  uploadSceneSaveModalRef.value?.show()
}
</script>

<template>
  <UploadSceneSaveModal ref="uploadSceneSaveModalRef"
                        @saveOk="(id) => setActiveId(id)" />
  <div class="u-main-content-no-padding">
    <SplitPanel default-size="160"
                min="160">
      <template #first>
        <div class="menu-sub-container">
          <div class="menu-sub-header">
            <div class="title">上传场景</div>
            <div>
              <t-button size="small"
                        theme="default"
                        @click="handleAddScene">
                <template #icon>
                  <t-icon class="i-u-plus"></t-icon>
                </template>
                添加
              </t-button>
            </div>
          </div>
          <Scrollbar style="height: 100%;">
            <div class="menu-sub">
              <div v-for="scene in sceneStore.sceneInfoList"
                   :key="scene.id"
                   class="u-fx u-fac u-gap10 menu-item"
                   style="justify-content: space-between"
                   :class="{ active: activeId === scene.id}"
                   @click="() => setActiveId(scene.id)">
                <div class="u-fx u-fac u-gap10" style="font-size: 13px">
                  <div>{{ scene.sceneName }}</div>
                </div>
                <div>
                  <t-dropdown size="small" placement="bottom">
                    <div class="i-u-more-one"></div>
                    <t-dropdown-menu>
                      <t-dropdown-item theme="error"
                                       @click="() => handleDeleteScene(scene)">
                        <template #prefixIcon>
                          <div class="i-u-delete"></div>
                        </template>
                        删除
                      </t-dropdown-item>
                    </t-dropdown-menu>
                  </t-dropdown>
                </div>
              </div>
            </div>
          </Scrollbar>
        </div>
      </template>
      <template #second>
        <UploadSceneFlowDetail
          v-if="activeId"
          :scene-id="activeId" />
        <div v-else>
          <Empty style="padding-top: 100px;"></Empty>
        </div>
      </template>
    </SplitPanel>
  </div>
</template>
