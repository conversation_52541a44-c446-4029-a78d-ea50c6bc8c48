<script setup lang="ts">
import { CONFIG, type ICleanContextData } from './CleanContextNode.ts'
import BaseFlowNode from '@/views/UploadScene/flow/node/BaseFlowNode.vue'
import type { IFlowNode } from '@/views/UploadScene/flow'
import { SettingItem, SwitchPlusEnable } from '@xiaou66/u-web-ui'

 defineProps<{
  node: IFlowNode<ICleanContextData>;
}>();
</script>

<template>
  <BaseFlowNode v-bind="CONFIG.info"
                :id="node.id">
    <SettingItem size="small"
                 :background="'transparent'"
                 :click="true"
                 @click="node!.nodeData.clearFileUploadInfo = !node.nodeData.clearFileUploadInfo">
      <template #title>
        <span class="text-sm">清空之前上传存储源内容</span>
      </template>
      <SwitchPlusEnable v-model:value="node!.nodeData.clearFileUploadInfo"
                        size="small"
                        style="width: 36px;"
                        @click.stop />
    </SettingItem>
  </BaseFlowNode>
</template>

<style scoped lang="less">

</style>
