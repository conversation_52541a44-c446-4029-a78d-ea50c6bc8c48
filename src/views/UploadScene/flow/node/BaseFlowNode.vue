<script setup lang="ts">
import type { IFlowNodeInfo } from './BaseFlowNode.ts';
import  { useFlowInject } from './BaseFlowNode.ts';
defineProps<IFlowNodeInfo & {id: string, disableDelete?: boolean}>();
const flowProvide = useFlowInject();
</script>

<template>
  <div class="flow">
    <t-collapse-panel :key="id">
      <template #header>
        <div class="w-full flex items-center justify-between">
          <div class="u-fx u-fac u-gap5">
            <div class="text-sm">{{ title }}</div>
            <div v-if="desc" class="u-tips">
              {{ desc }}
            </div>
          </div>
          <div class="flex gap-2 items-center action">
            <div class="u-tips">{{ id }}</div>
            <t-button v-if="!disableDelete"
                      size="small"
                      theme="danger"
                      variant="text"
                      @click.stop="flowProvide.removeNode(id)">
              <template #icon>
                <t-icon class="i-u-delete" color="rgb(var(--red-5))"></t-icon>
              </template>
            </t-button>
          </div>
        </div>
      </template>
      <div class="p-3">
        <slot name="default"></slot>
      </div>
    </t-collapse-panel>
  </div>
</template>

<style scoped lang="less">
body[arco-theme="dark"] {
  .flow {
    &:hover {
      //background: #3E3E3E;
    }
  }
}
.flow {
  padding: 2px;
  border: 2px solid transparent;
  border-radius: 10px;
  transition: all 200ms linear;
  .action {
    transition: all 320ms linear;
    opacity: 0;
  }

  &:hover {
    //background-color: #ffffff;
    border: 2px solid var(--u-color-neutral-3);
    .action {
      opacity: 1;
    }
  }
}
</style>
