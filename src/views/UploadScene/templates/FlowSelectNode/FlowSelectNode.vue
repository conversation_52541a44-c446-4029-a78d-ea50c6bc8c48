<script setup lang="ts">
import { FLOW_NODE_GROUP, type IFlowNode, SELECT_GROUP_SORT_LIST } from '@/views/UploadScene/flow'
import { useFlowInject } from '../../flow/node/BaseFlowNode.ts'
import { ContextMenu, ContextMenuSubmenu, ContextMenuItem } from '@xiaou66/u-web-ui'

defineModel<IFlowNode[]>('nodeList', {
  default: () => [],
})

const { createNode } = useFlowInject()
</script>

<template>
  <ContextMenu :trigger="['click', 'context-menu']">
    <div class="u-fx u-fc u-fac u-pointer flow-select-node">
      <div class="i-u-plus w-6 h-6"></div>
    </div>
    <template #content>
      <ContextMenuSubmenu v-for="groupKey in SELECT_GROUP_SORT_LIST"
                        :key="groupKey"
                        :label="groupKey">
        <ContextMenuItem  v-for="flowNode in FLOW_NODE_GROUP[groupKey]!"
                          :key="flowNode.info.code"
                          @click="createNode(flowNode)"
                          :label="flowNode.info.title"
                          :value="flowNode.info.code" />
      </ContextMenuSubmenu>
    </template>
  </ContextMenu>
</template>

<style scoped lang="less">
.flow-select-node {
  width: 100%;
  height: 28px;
  border: 2px dashed #ccc;
}
</style>
