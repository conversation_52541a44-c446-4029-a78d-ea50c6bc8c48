import type { StoragePlugInConfig, StoragePlugIn } from '@xiaou66/picture-plugin'
import { NotifyPlugin } from 'tdesign-vue-next'
import { usePluginStore } from '@/stores'
import sysPluginApi from '@/api/ucloud/SysPluginApi.ts'
import HttpUtils from '@/utils/HttpUtils'
import DataUtils from '@/utils/DataUtils.ts'
import FileUtils from '@/utils/FileUtils.ts'
import SysPluginApi from '@/api/ucloud/SysPluginApi.ts'

function asyncRequire<T>(modulePath: string): Promise<T> {
  return new Promise((resolve, reject) => {
    // @ts-ignore
    setTimeout(() => {
      try {
        const module = window.nodeRequire(modulePath)
        resolve(module)
      } catch (error) {
        reject(error)
      }
    })
  })
  // @ts-ignore
}

export class StoragePlugInManager {
  private __pluginPathMap: Map<string, string> = new Map()
  private __pluginsInstanceMap: Map<string, StoragePlugIn> = new Map()
  constructor() {}

  /**
   * 加载插件
   * @param pluginPath
   */
  async loadPlugin(pluginPath: string): Promise<StoragePlugInConfig> {
    const pluginConfig = await this.readPluginConfig(pluginPath)
    this.__pluginPathMap.set(pluginConfig.pluginInfo.pluginCode, pluginPath)
    this.__pluginsInstanceMap.delete(pluginConfig.pluginInfo.pluginCode)
    return pluginConfig
  }

  /**
   * 加载所有安装插件
   */
  async loadAllInstalledPlugin(): Promise<void> {
    if (!window.fs.existsSync(DataUtils.getPluginPath())) {
      return
    }

    const pluginPathList = window.fs
      .readdirSync(DataUtils.getPluginPath(), {
        withFileTypes: true,
      })
      .filter((item: any) => item.isDirectory())
      .map(({ name }: any) => name)
      .map((pluginCode: string) => window.path.join(DataUtils.getPluginPath(), pluginCode))

    for (const pluginPath of pluginPathList) {
      try {
        await this.loadPlugin(pluginPath)
      } catch (e: any) {
        await NotifyPlugin.error({
          title: '加载插件出现异常: ' + window.path.basename(pluginPath),
          content: e.message,
        })
        console.error(e)
      }
    }
  }

  /**
   * 卸载插件
   * @param plugCode
   */
  unLoadPlugin(plugCode: string): void {
    this.__pluginPathMap.delete(plugCode)
    if (this.__pluginsInstanceMap.has(plugCode)) {
      const instance = this.__pluginsInstanceMap.get(plugCode)!
      instance.destroy()
      instance.uninstall()
    }
  }

  /**
   * 获取插件配置
   * @param plugCode 插件 code
   */
  async getPluginConfig(plugCode: string): Promise<StoragePlugInConfig | undefined> {
    const pluginPath = this.__pluginPathMap.get(plugCode)
    if (!pluginPath) {
      return undefined
    }
    console.log('getPluginConfig', pluginPath, plugCode)
    try {
      return this.readPluginConfig(pluginPath)
    } catch (e) {
      return undefined
    }
  }

  /**
   * 获取插件实例
   * @param plugCode 插件 code
   */
  getPluginInstance(plugCode: string): StoragePlugIn {
    const storagePlugIn = this.__pluginsInstanceMap.get(plugCode)
    if (storagePlugIn) {
      return storagePlugIn
    }
    const pluginPath = this.__pluginPathMap.get(plugCode)
    if (!pluginPath) {
      NotifyPlugin.error({
        title: '获取插件实例',
        content: `缺少 ${plugCode} 插件`,
      })
      throw Error(`缺少 ${plugCode} 插件`)
    }
    const configPath = window.path.join(pluginPath, 'app.js')
    delete window.nodeRequire.cache[window.nodeRequire.resolve(configPath)]
    const storePlugInClass = window.nodeRequire(configPath)
    const storePlugIn: StoragePlugIn = new storePlugInClass()
    storePlugIn.init()

    // 创建代理对象来拦截方法执行异常
    const proxyStorePlugIn = new Proxy(storePlugIn, {
      get(target, prop) {
        // @ts-ignore
        const value = target[prop]
        if (typeof value === 'function') {
          return new Proxy(value, {
            apply(target, thisArg, args) {
              try {
                const result = target.apply(thisArg, args)
                if (result instanceof Promise) {
                  return result.catch((error) => {
                    NotifyPlugin.error({
                      title: '插件执行错误',
                      content: `${plugCode} 插件执行 ${String(prop)} 方法时发生错误: ${error.message}`,
                      closeBtn: true,
                    })
                    console.error(`Plugin ${plugCode} error:`, error)
                    return Promise.reject(error)
                  })
                }
                return result
              } catch (error: any) {
                NotifyPlugin.error({
                  title: '插件执行错误',
                  content: `${plugCode} 插件执行 ${String(prop)} 方法时发生错误: ${error.message}`,
                  closeBtn: true,
                })
                console.error(`Plugin ${plugCode} error:`, error)
                throw error
              }
            },
          })
        }
        return value
      },
    })

    this.__pluginsInstanceMap.set(plugCode, proxyStorePlugIn)
    return proxyStorePlugIn
  }

  /**
   * 读取插件配置
   * @param pluginPath
   */
  async readPluginConfig(pluginPath: string): Promise<StoragePlugInConfig> {
    console.log('getPluginConfig', pluginPath)
    const configPath = window.path.join(pluginPath, 'config.js')
    if (!window.fs.existsSync(configPath)) {
      NotifyPlugin.error({
        title: '加载插件错误通知',
        content: `${configPath} 无法加载配置文件`,
        closeBtn: true,
      })
      throw Error(`readPluginConfig--${configPath} 无法加载配置文件`)
    }
    delete window.nodeRequire.cache[window.nodeRequire.resolve(configPath)]
    const config = await asyncRequire<StoragePlugInConfig>(configPath)
    if (!config.pluginInfo.pluginLogo.startsWith('file://')) {
      config.pluginInfo.pluginLogo =
        'file://' + window.path.join(pluginPath, config.pluginInfo.pluginLogo)
    }
    return config
  }

  /**
   * 获取所有需要更新的插件 code
   */
  async getAllUpdatePluginList(): Promise<string[]> {
    if (this.__pluginPathMap.size === 0) {
      return []
    }
    const params = (await this.getAllPluginConfig()).map(
      ({ pluginInfo: { pluginCode, pluginVersion } }) => ({ pluginCode, pluginVersion }),
    )
    const response = await SysPluginApi.pluginCheckUpdateApi(params)
    return response.updatePluginList.map(({ pluginCode }) => pluginCode)
  }

  /**
   * 更新所有需要更新的插件
   */
  async updateAllPluginList() {
    const updatePluginCodeList = await this.getAllUpdatePluginList()
    for (const pluginCode of updatePluginCodeList) {
      await this.install(pluginCode)
    }
  }

  async getAllPluginConfig() {
    const configList: StoragePlugInConfig[] = []
    for (const plugCode of this.__pluginPathMap.keys()) {
      const pluginConfig = await this.getPluginConfig(plugCode)
      if (pluginConfig) {
        configList.push(pluginConfig)
      }
    }
    return configList
  }

  uninstallPlugin(pluginCode: any) {
    const pluginStore = usePluginStore()
    const pluginPath = this.__pluginPathMap.get(pluginCode)!
    if (pluginStore.isManualPlugin(pluginPath)) {
      this.unLoadPlugin(pluginCode)
      pluginStore.removeManualPlugin(pluginPath)
    } else {
      this.unLoadPlugin(pluginCode)
      // 市场插件
      window.fs.rmSync(window.path.join(DataUtils.getPluginPath(), pluginCode), {
        recursive: true,
        force: true,
      })
    }
  }

  isInstall(pluginCode: string) {
    return this.__pluginPathMap.has(pluginCode)
  }

  async install(pluginCode: string) {
    try {
      const response = await sysPluginApi.pluginDownloadUrlApi(pluginCode)
      const installedPluginFile = window.path.join(DataUtils.getPluginPath(), response.fileName)
      await HttpUtils.downloadFile(response.previewUrl, installedPluginFile)
      const pluginPath = window.path.join(DataUtils.getPluginPath(), pluginCode)
      if (window.fs.existsSync(pluginPath)) {
        debugger
        // // 如果已经存在删除, 实现更新
        window.fs.rmSync(pluginPath, {
          recursive: true,
          force: true,
        })
      }
      await FileUtils.unZip(installedPluginFile, pluginPath, true)
      await window.storagePlugInManager.loadPlugin(pluginPath)
    } catch (e: any) {
      NotifyPlugin.error({
        title: '插件安装失败',
        content: e.message,
        closeBtn: true,
      })
      throw e
    }
  }
}
