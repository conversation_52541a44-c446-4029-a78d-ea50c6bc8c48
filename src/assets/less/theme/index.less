@import "theme";
.menu-sub-container {
  box-sizing: border-box;
  padding: 4px;
  height: 100%;
  background: var(--u-bg-color-3);
  .menu-sub-header {
    --at-apply: flex items-center justify-between;
    margin-bottom: 10px;
    .title {
      --at-apply: pl-4 text-sm;
      font-weight: 500;
    }
  }
  // 二级菜单
  .menu-sub {
    display: flex;
    flex-direction: column;
    gap: 4px;
    .menu-item {
      padding: 8px 12px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 300ms ease;
      &:hover {
        background-color: var(--select-hover);
      }

      // 选中状态
      &.active {
        background-color: rgba(var(--blue-6), 0.05);
        color: rgba(var(--blue-5));
      }
    }
  }
}


