:root {
  --text-color: #31322c;
  --main-background:  #FAFBFB;
  --main-background-transparent: #FAFBFB;
  // 实色
  --main-ui-background: #FAFBFB;
  --u-hover-color: #f5f5f5;
  --select-hover: rgba(225, 225, 225, 0.2);
  --select-selected: #ffffff;
  --select-selested-text-color: #0600FF;
  --utools-background: #F4F4F4;
  --text-tips-color: #bdc3c7;
  --tool-attr-select-color: #bdc3c7;
  --tool-box-shadow: none;
  // FQA 分类
  --faq-background-color: #54a0ff;
  --faq-background-hover-color: rgba(84, 160, 255, 0.8);
  --faq-highlight-background-color: #FEF8E8;
  // card
  --card-background-color: #F3F5FC;
  --left-menu-background-color: rgba(247, 248, 250, 1);

}
:root, :root[theme-mode="light"] {
  --td-bg-color-component: rgb(var(--gray-2));
  --td-bg-color-specialcomponent: rgb(var(--gray-2));
}

// 输入框
.t-input__wrap {
  .t-input {
    border-color: transparent;
    transition: background 250ms linear;
  }
  .t-input:hover {
    background: rgb(var(--gray-3));
    border-color: transparent;
  }
  .t-input:hover, .t-input--focused {
    border-color: var(--td-brand-color);
    background: var(--u-bg-color-3);
  }
}
// 按钮
.u-web-transparent {
  background: transparent;
  .t-input {
    background: transparent;
  }
  .t-input.t-is-readonly {
    background: transparent;
  }
  &.t-button--variant-outline {
    background: transparent;
    &:hover {
      background: transparent;
    }
  }
}



.u-web-popup {
  .t-popup__content {
    padding: 0 !important;
    margin: 0 !important;
  }
}
.t-dialog__footer {
  padding-top: 6px !important;
}
