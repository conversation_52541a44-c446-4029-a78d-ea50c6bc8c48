import { AsyncComponentLoader, AsyncComponentOptions } from 'vue'

export interface StorageUIFormElement {
  type: 'input' | 'input-format' | 'radio-group' | 'switch' | 'input-button' | 'select' | 'custom';
  customComponent?: AsyncComponentLoader | AsyncComponentOptions;
  formItem: {
    rules?: Record<string, any>[];
    name: string;
    label: string;
    [key: string]: any;
  },

  /**
   * 元素属性值
   */
  elementProperty: Record<string, any>;
  /**
   * 元素事件
   */
  elementEvent?: Record<string, any>;
}

export interface StorageUIConfig {
  /**
   * 提示
   */
  tips: string,

  /**
   * 表单
   */
  forms: StorageUIFormElement[];
}

/**
 * 插件信息
 */
export interface PlugInfo {
  /**
   * 插件 code 全局唯一
   */
  pluginCode: string;
  /**
   * 插件名称
   */
  pluginName: string;

  /**
   * 插件版本
   */
  pluginVersion: string;

  /**
   * 插件作者
   */
  pluginAuthor: string;

  /**
   * 插件描述
   */
  pluginDesc: string;

  /**
   * 插件类型
   */
  pluginType: 'storage';

  /**
   * 插件 logo
   */
  pluginLogo: string;

  /**
   * 插件分组
   */
  pluginGroup: 'cloudVendor' | 'self' | 'other';
}
export interface StoragePlugInConfig {

  /**
   * 无需配置可以直接使用
   */
  noConfig?: boolean;

  pluginInfo: PlugInfo;

  /**
   * 配置 ui
   */
  uiConfig?: StorageUIConfig;
}

/**
 * 场景插件配置
 * @param config
 */
export function createStoragePlugInConfig(config: Omit<StoragePlugInConfig, 'pluginInfo'> & { pluginInfo: Omit<PlugInfo, 'pluginType'> }): StoragePlugInConfig {
  const newConfig = config as StoragePlugInConfig
  newConfig.pluginInfo.pluginType = 'storage';
  return newConfig;
}
