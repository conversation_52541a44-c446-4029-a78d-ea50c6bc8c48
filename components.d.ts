/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    CopyUrlFormatSelect: typeof import('./src/components/business/CopyUrlFormatSelect.vue')['default']
    FileIconPreview: typeof import('./src/components/FilePreview/FileIconPreview.vue')['default']
    FilePreview: typeof import('./src/components/FilePreview/FilePreview.vue')['default']
    FileUpload: typeof import('./src/components/FileUpload/FileUpload.vue')['default']
    FileUploadImageItem: typeof import('./src/components/FileItem/FileUploadImageItem/FileUploadImageItem.vue')['default']
    FileUploadingItem: typeof import('./src/components/FileItem/FileUploadingItem/FileUploadingItem.vue')['default']
    FileUploadLineItem: typeof import('./src/components/FileItem/FileUploadLineItem/FileUploadLineItem.vue')['default']
    GuideUse: typeof import('./src/components/GuideUse/GuideUse.vue')['default']
    InputButton: typeof import('./src/components/plugin/Button/InputButton.vue')['default']
    InputFileFormat: typeof import('./src/components/plugin/InputFormat/InputFileFormat.vue')['default']
    InputFormat: typeof import('./src/components/plugin/InputFormat/InputFormat.vue')['default']
    LazyImg: typeof import('./src/components/FilePreview/preview/LazyImg.vue')['default']
    RadioGroup: typeof import('./src/components/plugin/RadioGroup/RadioGroup.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SelectSearch: typeof import('./src/components/plugin/select/SelectSearch.vue')['default']
    TAlert: typeof import('tdesign-vue-next')['Alert']
    TButton: typeof import('tdesign-vue-next')['Button']
    TCascader: typeof import('tdesign-vue-next')['Cascader']
    TCheckbox: typeof import('tdesign-vue-next')['Checkbox']
    TCollapse: typeof import('tdesign-vue-next')['Collapse']
    TCollapsePanel: typeof import('tdesign-vue-next')['CollapsePanel']
    TDialog: typeof import('tdesign-vue-next')['Dialog']
    TDrawer: typeof import('tdesign-vue-next')['Drawer']
    TDropdown: typeof import('tdesign-vue-next')['Dropdown']
    TDropdownItem: typeof import('tdesign-vue-next')['DropdownItem']
    TDropdownMenu: typeof import('tdesign-vue-next')['DropdownMenu']
    TEmpty: typeof import('tdesign-vue-next')['Empty']
    TForm: typeof import('tdesign-vue-next')['Form']
    TFormItem: typeof import('tdesign-vue-next')['FormItem']
    TIcon: typeof import('tdesign-vue-next')['Icon']
    TInput: typeof import('tdesign-vue-next')['Input']
    TInputGroup: typeof import('tdesign-vue-next')['InputGroup']
    TInputNumber: typeof import('tdesign-vue-next')['InputNumber']
    TLink: typeof import('tdesign-vue-next')['Link']
    TLoading: typeof import('tdesign-vue-next')['Loading']
    TOption: typeof import('tdesign-vue-next')['Option']
    TPagination: typeof import('tdesign-vue-next')['Pagination']
    TPopconfirm: typeof import('tdesign-vue-next')['Popconfirm']
    TPopup: typeof import('tdesign-vue-next')['Popup']
    TRadioButton: typeof import('tdesign-vue-next')['RadioButton']
    TRadioGroup: typeof import('tdesign-vue-next')['RadioGroup']
    TSelect: typeof import('tdesign-vue-next')['Select']
    TTable: typeof import('tdesign-vue-next')['Table']
    TTag: typeof import('tdesign-vue-next')['Tag']
    TTooltip: typeof import('tdesign-vue-next')['Tooltip']
    WheelWaveContainer: typeof import('./src/components/common/container/WheelWaveContainer/WheelWaveContainer.vue')['default']
  }
}
